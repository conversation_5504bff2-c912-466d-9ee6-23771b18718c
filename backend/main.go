package main

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	//"github.com/gogf/gf/v2/os/gtimer"
)

// 资产统计结构体
// 可根据实际API返回结构调整
// 这里只做演示

type AssetStat struct {
	ID          int    `json:"id" orm:"id"`
	CountryName string `json:"country_name" orm:"country_name"`
	CountryCode string `json:"country_code" orm:"country_code"`
	AssetCount  int64  `json:"asset_count" orm:"asset_count"`
	StatDate    string `json:"stat_date" orm:"stat_date"`
	CreatedAt   string `json:"created_at" orm:"created_at"`
}

type FofaCountry struct {
	Code     string `json:"code"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
	NameCode string `json:"name_code"`
}

type FofaAggs struct {
	Countries []FofaCountry `json:"countries"`
}

type FofaResp struct {
	Error          bool     `json:"error"`
	Aggs           FofaAggs `json:"aggs"`
	Query          string   `json:"query"`
	LastUpdateTime string   `json:"lastupdatetime"`
}

func fetchAndStoreFofaStats(ctx context.Context) error {
	// 使用正确的API调用格式，查询所有国家的资产统计
	// 查询条件: port!="" (所有有端口的资产)
	// qbase64 是 base64 编码的查询条件
	url := "https://fofa.info/api/v1/search/stats?fields=country&key=a5badb45e12cd17414ebae4ea333c4f9&qbase64=cG9ydCE9IiIg"

	g.Log().Info(ctx, "正在调用FOFA API...")

	resp, err := http.Get(url)
	if err != nil {
		return gerror.Newf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return gerror.Newf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var fofaResp FofaResp
	if err := json.NewDecoder(resp.Body).Decode(&fofaResp); err != nil {
		return gerror.Newf("JSON解析失败: %v", err)
	}

	g.Log().Infof(ctx, "FOFA API响应: error=%v, countries_count=%d",
		fofaResp.Error, len(fofaResp.Aggs.Countries))

	if fofaResp.Error {
		return gerror.New("FOFA API返回错误状态")
	}

	if len(fofaResp.Aggs.Countries) == 0 {
		g.Log().Warning(ctx, "FOFA API返回的国家数据为空，可能是查询条件问题或数据暂时不可用")
		return nil // 不返回错误，只是警告
	}

	today := time.Now().Format("2006-01-02")
	successCount := 0

	for _, c := range fofaResp.Aggs.Countries {
		g.Log().Infof(ctx, "插入国家数据: %s (%s) - %d", c.Name, c.NameCode, c.Count)

		_, err := g.DB().Model("asset_stats").Data(g.Map{
			"country_name": c.Name,
			"country_code": c.NameCode,
			"asset_count":  c.Count,
			"stat_date":    today,
		}).Insert()
		if err != nil {
			g.Log().Error(ctx, "数据库插入失败: ", err)
		} else {
			successCount++
		}
	}

	g.Log().Infof(ctx, "数据插入完成，成功插入%d条记录", successCount)
	return nil
}

func main() {
	ctx := context.Background()

	// 定时任务：每天0点拉取并存储数据
	// gtimer.AddSingleton(ctx, time.Minute, func(ctx context.Context) {
	// 	now := time.Now()
	// 	if now.Hour() == 0 && now.Minute() == 0 {
	// 		err := fetchAndStoreFofaStats(ctx)
	// 		if err != nil {
	// 			g.Log().Error(ctx, "定时任务失败: ", err)
	// 		}
	// 	}
	// })

	// 启动时执行一次数据拉取（可选，取消注释以启用）
	// err := fetchAndStoreFofaStats(ctx)
	// if err != nil {
	// 	g.Log().Error(ctx, "定时任务失败: ", err)
	// }

	// HTTP接口：按国家返回历史数据
	s := ghttp.GetServer()
	s.SetPort(8001)
	s.BindHandler("/api/assets", func(r *ghttp.Request) {
		country := r.Get("country").String()
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Where("country_code = ?", country).
			Order("stat_date asc").
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}

		// 转换为历史数据格式
		var history []g.Map
		for _, item := range result {
			history = append(history, g.Map{
				"stat_date":    item.StatDate,
				"asset_count":  item.AssetCount,
			})
		}

		r.Response.WriteJson(g.Map{
			"country": country,
			"history": history,
		})
	})

	// 添加调试接口查看所有数据
	s.BindHandler("/api/debug/all", func(r *ghttp.Request) {
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Order("created_at desc").
			Limit(20).
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}
		r.Response.WriteJson(g.Map{
			"total": len(result),
			"data":  result,
		})
	})

	s.Run()
}
