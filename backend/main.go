package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	//"github.com/gogf/gf/v2/os/gtimer"
)

// 资产统计结构体
// 可根据实际API返回结构调整
// 这里只做演示

type AssetStat struct {
	ID          int    `json:"id" orm:"id"`
	CountryName string `json:"country_name" orm:"country_name"`
	CountryCode string `json:"country_code" orm:"country_code"`
	AssetCount  int64  `json:"asset_count" orm:"asset_count"`
	StatDate    string `json:"stat_date" orm:"stat_date"`
	CreatedAt   string `json:"created_at" orm:"created_at"`
}

type FofaCountry struct {
	Code     string `json:"code"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
	NameCode string `json:"name_code"`
}

type FofaAggs struct {
	Countries []FofaCountry `json:"countries"`
}

type FofaResp struct {
	Error          bool     `json:"error"`
	Aggs           FofaAggs `json:"aggs"`
	Query          string   `json:"query"`
	LastUpdateTime string   `json:"lastupdatetime"`
}

// 国家映射信息结构体
type MapNameInfo struct {
	Code  string `json:"code"`
	CName string `json:"cname"`
	EName string `json:"ename"`
}

// 全球国家和地区映射
var CountryMap = map[string]MapNameInfo{
	"CN": {Code: "CN", CName: "中国", EName: "China"},
	"TW": {Code: "TW", CName: "中国台湾省", EName: "Taiwan, Province of China"},
	"HK": {Code: "HK", CName: "中国香港特别行政区", EName: "Hong Kong Special Administrative Region"},
	"MO": {Code: "MO", CName: "中国澳门特别行政区", EName: "Macao Special Administration Region"},
	"AF": {Code: "AF", CName: "阿富汗", EName: "Afghanistan"},
	"AL": {Code: "AL", CName: "阿尔巴尼亚", EName: "Albania"},
	"DZ": {Code: "DZ", CName: "阿尔及利亚", EName: "Algeria"},
	"AS": {Code: "AS", CName: "美属萨摩亚", EName: "American Samoa"},
	"AD": {Code: "AD", CName: "安道尔", EName: "Andorra"},
	"AO": {Code: "AO", CName: "安哥拉", EName: "Angola"},
	"AI": {Code: "AI", CName: "安圭拉", EName: "Anguilla"},
	"AQ": {Code: "AQ", CName: "南极洲", EName: "Antarctica"},
	"AG": {Code: "AG", CName: "安提瓜和巴布达", EName: "Antigua and Barbuda"},
	"AR": {Code: "AR", CName: "阿根廷", EName: "Argentina"},
	"AM": {Code: "AM", CName: "亚美尼亚", EName: "Armenia"},
	"AW": {Code: "AW", CName: "阿鲁巴", EName: "Aruba"},
	"AU": {Code: "AU", CName: "澳大利亚", EName: "Australia"},
	"AT": {Code: "AT", CName: "奥地利", EName: "Austria"},
	"AZ": {Code: "AZ", CName: "阿塞拜疆", EName: "Azerbaijan"},
	"BS": {Code: "BS", CName: "巴哈马", EName: "Bahamas"},
	"BH": {Code: "BH", CName: "巴林", EName: "Bahrain"},
	"BD": {Code: "BD", CName: "孟加拉国", EName: "Bangladesh"},
	"BB": {Code: "BB", CName: "巴巴多斯", EName: "Barbados"},
	"BY": {Code: "BY", CName: "白俄罗斯", EName: "Belarus"},
	"BE": {Code: "BE", CName: "比利时", EName: "Belgium"},
	"BZ": {Code: "BZ", CName: "伯利兹", EName: "Belize"},
	"BJ": {Code: "BJ", CName: "贝宁", EName: "Benin"},
	"BM": {Code: "BM", CName: "百慕大", EName: "Bermuda"},
	"BT": {Code: "BT", CName: "不丹", EName: "Bhutan"},
	"BO": {Code: "BO", CName: "玻利维亚", EName: "Bolivia (Plurinational State of)"},
	"BQ": {Code: "BQ", CName: "荷兰加勒比区", EName: "Bonaire, Sint Eustatius and Saba"},
	"BA": {Code: "BA", CName: "波黑", EName: "Bosnia and Herzegovina"},
	"BW": {Code: "BW", CName: "博茨瓦纳", EName: "Botswana"},
	"BV": {Code: "BV", CName: "布韦岛", EName: "Bouvet Island"},
	"BR": {Code: "BR", CName: "巴西", EName: "Brazil"},
	"IO": {Code: "IO", CName: "英属印度洋领地", EName: "British Indian Ocean Territory"},
	"BN": {Code: "BN", CName: "文莱", EName: "Brunei Darussalam"},
	"BG": {Code: "BG", CName: "保加利亚", EName: "Bulgaria"},
	"BF": {Code: "BF", CName: "布基纳法索", EName: "Burkina Faso"},
	"BI": {Code: "BI", CName: "布隆迪", EName: "Burundi"},
	"CV": {Code: "CV", CName: "佛得角", EName: "Cabo Verde"},
	"KH": {Code: "KH", CName: "柬埔寨", EName: "Cambodia"},
	"CM": {Code: "CM", CName: "喀麦隆", EName: "Cameroon"},
	"CA": {Code: "CA", CName: "加拿大", EName: "Canada"},
	"KY": {Code: "KY", CName: "开曼群岛", EName: "Cayman Islands"},
	"CF": {Code: "CF", CName: "中非", EName: "Central African Republic"},
	"TD": {Code: "TD", CName: "乍得", EName: "Chad"},
	"CL": {Code: "CL", CName: "智利", EName: "Chile"},
	"CX": {Code: "CX", CName: "圣诞岛", EName: "Christmas Island"},
	"CC": {Code: "CC", CName: "科科斯（基林）群岛", EName: "Cocos (Keeling) Islands"},
	"CO": {Code: "CO", CName: "哥伦比亚", EName: "Colombia"},
	"KM": {Code: "KM", CName: "科摩罗", EName: "Comoros"},
	"CG": {Code: "CG", CName: "刚果共和国", EName: "Congo"},
	"CD": {Code: "CD", CName: "刚果民主共和国", EName: "Congo (Democratic Republic of the)"},
	"CK": {Code: "CK", CName: "库克群岛", EName: "Cook Islands"},
	"CR": {Code: "CR", CName: "哥斯达黎加", EName: "Costa Rica"},
	"HR": {Code: "HR", CName: "克罗地亚", EName: "Croatia"},
	"CU": {Code: "CU", CName: "古巴", EName: "Cuba"},
	"CW": {Code: "CW", CName: "库拉索", EName: "Curaçao"},
	"CY": {Code: "CY", CName: "塞浦路斯", EName: "Cyprus"},
	"CZ": {Code: "CZ", CName: "捷克", EName: "Czechia"},
	"CI": {Code: "CI", CName: "科特迪瓦", EName: "Côte d'Ivoire"},
	"DK": {Code: "DK", CName: "丹麦", EName: "Denmark"},
	"DJ": {Code: "DJ", CName: "吉布提", EName: "Djibouti"},
	"DM": {Code: "DM", CName: "多米尼克", EName: "Dominica"},
	"DO": {Code: "DO", CName: "多米尼加", EName: "Dominican Republic"},
	"EC": {Code: "EC", CName: "厄瓜多尔", EName: "Ecuador"},
	"EG": {Code: "EG", CName: "埃及", EName: "Egypt"},
	"SV": {Code: "SV", CName: "萨尔瓦多", EName: "El Salvador"},
	"GQ": {Code: "GQ", CName: "赤道几内亚", EName: "Equatorial Guinea"},
	"ER": {Code: "ER", CName: "厄立特里亚", EName: "Eritrea"},
	"EE": {Code: "EE", CName: "爱沙尼亚", EName: "Estonia"},
	"SZ": {Code: "SZ", CName: "斯威士兰", EName: "Eswatini"},
	"ET": {Code: "ET", CName: "埃塞俄比亚", EName: "Ethiopia"},
	"FK": {Code: "FK", CName: "福克兰群岛", EName: "Falkland Islands (Malvinas)"},
	"FO": {Code: "FO", CName: "法罗群岛", EName: "Faroe Islands"},
	"FJ": {Code: "FJ", CName: "斐济", EName: "Fiji"},
	"FI": {Code: "FI", CName: "芬兰", EName: "Finland"},
	"FR": {Code: "FR", CName: "法国", EName: "France"},
	"GF": {Code: "GF", CName: "法属圭亚那", EName: "French Guiana"},
	"PF": {Code: "PF", CName: "法属波利尼西亚", EName: "French Polynesia"},
	"TF": {Code: "TF", CName: "法属南部和南极领地", EName: "French Southern Territories"},
	"GA": {Code: "GA", CName: "加蓬", EName: "Gabon"},
	"GM": {Code: "GM", CName: "冈比亚", EName: "Gambia"},
	"GE": {Code: "GE", CName: "格鲁吉亚", EName: "Georgia"},
	"DE": {Code: "DE", CName: "德国", EName: "Germany"},
	"GH": {Code: "GH", CName: "加纳", EName: "Ghana"},
	"GI": {Code: "GI", CName: "直布罗陀", EName: "Gibraltar"},
	"GR": {Code: "GR", CName: "希腊", EName: "Greece"},
	"GL": {Code: "GL", CName: "格陵兰", EName: "Greenland"},
	"GD": {Code: "GD", CName: "格林纳达", EName: "Grenada"},
	"GP": {Code: "GP", CName: "瓜德罗普", EName: "Guadeloupe"},
	"GU": {Code: "GU", CName: "关岛", EName: "Guam"},
	"GT": {Code: "GT", CName: "危地马拉", EName: "Guatemala"},
	"GG": {Code: "GG", CName: "根西", EName: "Guernsey"},
	"GN": {Code: "GN", CName: "几内亚", EName: "Guinea"},
	"GW": {Code: "GW", CName: "几内亚比绍", EName: "Guinea-Bissau"},
	"GY": {Code: "GY", CName: "圭亚那", EName: "Guyana"},
	"HT": {Code: "HT", CName: "海地", EName: "Haiti"},
	"HM": {Code: "HM", CName: "赫德岛和麦克唐纳群岛", EName: "Heard Island and McDonald Islands"},
	"VA": {Code: "VA", CName: "梵蒂冈", EName: "Holy See"},
	"HN": {Code: "HN", CName: "洪都拉斯", EName: "Honduras"},
	"HU": {Code: "HU", CName: "匈牙利", EName: "Hungary"},
	"IS": {Code: "IS", CName: "冰岛", EName: "Iceland"},
	"IN": {Code: "IN", CName: "印度", EName: "India"},
	"ID": {Code: "ID", CName: "印尼", EName: "Indonesia"},
	"IR": {Code: "IR", CName: "伊朗", EName: "Iran (Islamic Republic of)"},
	"IQ": {Code: "IQ", CName: "伊拉克", EName: "Iraq"},
	"IE": {Code: "IE", CName: "爱尔兰", EName: "Ireland"},
	"IM": {Code: "IM", CName: "马恩岛", EName: "Isle of Man"},
	"IL": {Code: "IL", CName: "以色列", EName: "Israel"},
	"IT": {Code: "IT", CName: "意大利", EName: "Italy"},
	"JM": {Code: "JM", CName: "牙买加", EName: "Jamaica"},
	"JP": {Code: "JP", CName: "日本", EName: "Japan"},
	"JE": {Code: "JE", CName: "泽西", EName: "Jersey"},
	"JO": {Code: "JO", CName: "约旦", EName: "Jordan"},
	"KZ": {Code: "KZ", CName: "哈萨克斯坦", EName: "Kazakhstan"},
	"KE": {Code: "KE", CName: "肯尼亚", EName: "Kenya"},
	"KI": {Code: "KI", CName: "基里巴斯", EName: "Kiribati"},
	"KP": {Code: "KP", CName: "朝鲜", EName: "Korea (Democratic People's Republic of)"},
	"KR": {Code: "KR", CName: "韩国", EName: "Korea (Republic of)"},
	"KW": {Code: "KW", CName: "科威特", EName: "Kuwait"},
	"KG": {Code: "KG", CName: "吉尔吉斯斯坦", EName: "Kyrgyzstan"},
	"LA": {Code: "LA", CName: "老挝", EName: "Lao People's Democratic Republic"},
	"LV": {Code: "LV", CName: "拉脱维亚", EName: "Latvia"},
	"LB": {Code: "LB", CName: "黎巴嫩", EName: "Lebanon"},
	"LS": {Code: "LS", CName: "莱索托", EName: "Lesotho"},
	"LR": {Code: "LR", CName: "利比里亚", EName: "Liberia"},
	"LY": {Code: "LY", CName: "利比亚", EName: "Libya"},
	"LI": {Code: "LI", CName: "列支敦士登", EName: "Liechtenstein"},
	"LT": {Code: "LT", CName: "立陶宛", EName: "Lithuania"},
	"LU": {Code: "LU", CName: "卢森堡", EName: "Luxembourg"},
	"MG": {Code: "MG", CName: "马达加斯加", EName: "Madagascar"},
	"MW": {Code: "MW", CName: "马拉维", EName: "Malawi"},
	"MY": {Code: "MY", CName: "马来西亚", EName: "Malaysia"},
	"MV": {Code: "MV", CName: "马尔代夫", EName: "Maldives"},
	"ML": {Code: "ML", CName: "马里", EName: "Mali"},
	"MT": {Code: "MT", CName: "马耳他", EName: "Malta"},
	"MH": {Code: "MH", CName: "马绍尔群岛", EName: "Marshall Islands"},
	"MQ": {Code: "MQ", CName: "马提尼克", EName: "Martinique"},
	"MR": {Code: "MR", CName: "毛里塔尼亚", EName: "Mauritania"},
	"MU": {Code: "MU", CName: "毛里求斯", EName: "Mauritius"},
	"YT": {Code: "YT", CName: "马约特", EName: "Mayotte"},
	"MX": {Code: "MX", CName: "墨西哥", EName: "Mexico"},
	"FM": {Code: "FM", CName: "密克罗尼西亚联邦", EName: "Micronesia (Federated States of)"},
	"MD": {Code: "MD", CName: "摩尔多瓦", EName: "Moldova (Republic of)"},
	"MC": {Code: "MC", CName: "摩纳哥", EName: "Monaco"},
	"MN": {Code: "MN", CName: "蒙古", EName: "Mongolia"},
	"ME": {Code: "ME", CName: "黑山", EName: "Montenegro"},
	"MS": {Code: "MS", CName: "蒙特塞拉特", EName: "Montserrat"},
	"MA": {Code: "MA", CName: "摩洛哥", EName: "Morocco"},
	"MZ": {Code: "MZ", CName: "莫桑比克", EName: "Mozambique"},
	"MM": {Code: "MM", CName: "缅甸", EName: "Myanmar"},
	"NA": {Code: "NA", CName: "纳米比亚", EName: "Namibia"},
	"NR": {Code: "NR", CName: "瑙鲁", EName: "Nauru"},
	"NP": {Code: "NP", CName: "尼泊尔", EName: "Nepal"},
	"NL": {Code: "NL", CName: "荷兰", EName: "Netherlands"},
	"NC": {Code: "NC", CName: "新喀里多尼亚", EName: "New Caledonia"},
	"NZ": {Code: "NZ", CName: "新西兰", EName: "New Zealand"},
	"NI": {Code: "NI", CName: "尼加拉瓜", EName: "Nicaragua"},
	"NE": {Code: "NE", CName: "尼日尔", EName: "Niger"},
	"NG": {Code: "NG", CName: "尼日利亚", EName: "Nigeria"},
	"NU": {Code: "NU", CName: "纽埃", EName: "Niue"},
	"NF": {Code: "NF", CName: "诺福克岛", EName: "Norfolk Island"},
	"MK": {Code: "MK", CName: "北马其顿", EName: "North Macedonia"},
	"MP": {Code: "MP", CName: "北马里亚纳群岛", EName: "Northern Mariana Islands"},
	"NO": {Code: "NO", CName: "挪威", EName: "Norway"},
	"OM": {Code: "OM", CName: "阿曼", EName: "Oman"},
	"PK": {Code: "PK", CName: "巴基斯坦", EName: "Pakistan"},
	"PW": {Code: "PW", CName: "帕劳", EName: "Palau"},
	"PS": {Code: "PS", CName: "巴勒斯坦", EName: "Palestine, State of"},
	"PA": {Code: "PA", CName: "巴拿马", EName: "Panama"},
	"PG": {Code: "PG", CName: "巴布亚新几内亚", EName: "Papua New Guinea"},
	"PY": {Code: "PY", CName: "巴拉圭", EName: "Paraguay"},
	"PE": {Code: "PE", CName: "秘鲁", EName: "Peru"},
	"PH": {Code: "PH", CName: "菲律宾", EName: "Philippines"},
	"PN": {Code: "PN", CName: "皮特凯恩群岛", EName: "Pitcairn"},
	"PL": {Code: "PL", CName: "波兰", EName: "Poland"},
	"PT": {Code: "PT", CName: "葡萄牙", EName: "Portugal"},
	"PR": {Code: "PR", CName: "波多黎各", EName: "Puerto Rico"},
	"QA": {Code: "QA", CName: "卡塔尔", EName: "Qatar"},
	"RO": {Code: "RO", CName: "罗马尼亚", EName: "Romania"},
	"RU": {Code: "RU", CName: "俄罗斯", EName: "Russian Federation"},
	"RW": {Code: "RW", CName: "卢旺达", EName: "Rwanda"},
	"RE": {Code: "RE", CName: "留尼汪", EName: "Réunion"},
	"BL": {Code: "BL", CName: "圣巴泰勒米", EName: "Saint Barthélemy"},
	"SH": {Code: "SH", CName: "圣赫勒拿、阿森松和特里斯坦-达库尼亚", EName: "Saint Helena, Ascension and Tristan da Cunha"},
	"KN": {Code: "KN", CName: "圣基茨和尼维斯", EName: "Saint Kitts and Nevis"},
	"LC": {Code: "LC", CName: "圣卢西亚", EName: "Saint Lucia"},
	"MF": {Code: "MF", CName: "法属圣马丁", EName: "Saint Martin (French part)"},
	"PM": {Code: "PM", CName: "圣皮埃尔和密克隆", EName: "Saint Pierre and Miquelon"},
	"VC": {Code: "VC", CName: "圣文森特和格林纳丁斯", EName: "Saint Vincent and the Grenadines"},
	"WS": {Code: "WS", CName: "萨摩亚", EName: "Samoa"},
	"SM": {Code: "SM", CName: "圣马力诺", EName: "San Marino"},
	"ST": {Code: "ST", CName: "圣多美和普林西比", EName: "Sao Tome and Principe"},
	"SA": {Code: "SA", CName: "沙特阿拉伯", EName: "Saudi Arabia"},
	"SN": {Code: "SN", CName: "塞内加尔", EName: "Senegal"},
	"RS": {Code: "RS", CName: "塞尔维亚", EName: "Serbia"},
	"SC": {Code: "SC", CName: "塞舌尔", EName: "Seychelles"},
	"SL": {Code: "SL", CName: "塞拉利昂", EName: "Sierra Leone"},
	"SG": {Code: "SG", CName: "新加坡", EName: "Singapore"},
	"SX": {Code: "SX", CName: "荷属圣马丁", EName: "Sint Maarten (Dutch part)"},
	"SK": {Code: "SK", CName: "斯洛伐克", EName: "Slovakia"},
	"SI": {Code: "SI", CName: "斯洛文尼亚", EName: "Slovenia"},
	"SB": {Code: "SB", CName: "所罗门群岛", EName: "Solomon Islands"},
	"SO": {Code: "SO", CName: "索马里", EName: "Somalia"},
	"ZA": {Code: "ZA", CName: "南非", EName: "South Africa"},
	"GS": {Code: "GS", CName: "南乔治亚和南桑威奇群岛", EName: "South Georgia and the South Sandwich Islands"},
	"SS": {Code: "SS", CName: "南苏丹", EName: "South Sudan"},
	"ES": {Code: "ES", CName: "西班牙", EName: "Spain"},
	"LK": {Code: "LK", CName: "斯里兰卡", EName: "Sri Lanka"},
	"SD": {Code: "SD", CName: "苏丹", EName: "Sudan"},
	"SR": {Code: "SR", CName: "苏里南", EName: "Suriname"},
	"SJ": {Code: "SJ", CName: "斯瓦尔巴和扬马延", EName: "Svalbard and Jan Mayen"},
	"SE": {Code: "SE", CName: "瑞典", EName: "Sweden"},
	"CH": {Code: "CH", CName: "瑞士", EName: "Switzerland"},
	"SY": {Code: "SY", CName: "叙利亚", EName: "Syrian Arab Republic"},
	"TJ": {Code: "TJ", CName: "塔吉克斯坦", EName: "Tajikistan"},
	"TZ": {Code: "TZ", CName: "坦桑尼亚", EName: "Tanzania, United Republic of"},
	"TH": {Code: "TH", CName: "泰国", EName: "Thailand"},
	"TL": {Code: "TL", CName: "东帝汶", EName: "Timor-Leste"},
	"TG": {Code: "TG", CName: "多哥", EName: "Togo"},
	"TK": {Code: "TK", CName: "托克劳", EName: "Tokelau"},
	"TO": {Code: "TO", CName: "汤加", EName: "Tonga"},
	"TT": {Code: "TT", CName: "特立尼达和多巴哥", EName: "Trinidad and Tobago"},
	"TN": {Code: "TN", CName: "突尼斯", EName: "Tunisia"},
	"TR": {Code: "TR", CName: "土耳其", EName: "Turkey"},
	"TM": {Code: "TM", CName: "土库曼斯坦", EName: "Turkmenistan"},
	"TC": {Code: "TC", CName: "特克斯和凯科斯群岛", EName: "Turks and Caicos Islands"},
	"TV": {Code: "TV", CName: "图瓦卢", EName: "Tuvalu"},
	"UG": {Code: "UG", CName: "乌干达", EName: "Uganda"},
	"UA": {Code: "UA", CName: "乌克兰", EName: "Ukraine"},
	"AE": {Code: "AE", CName: "阿联酋", EName: "United Arab Emirates"},
	"GB": {Code: "GB", CName: "英国", EName: "United Kingdom of Great Britain and Northern Ireland"},
	"UM": {Code: "UM", CName: "美国本土外小岛屿", EName: "United States Minor Outlying Islands"},
	"US": {Code: "US", CName: "美国", EName: "United States of America"},
	"UY": {Code: "UY", CName: "乌拉圭", EName: "Uruguay"},
	"UZ": {Code: "UZ", CName: "乌兹别克斯坦", EName: "Uzbekistan"},
	"VU": {Code: "VU", CName: "瓦努阿图", EName: "Vanuatu"},
	"VE": {Code: "VE", CName: "委内瑞拉", EName: "Venezuela (Bolivarian Republic of)"},
	"VN": {Code: "VN", CName: "越南", EName: "Viet Nam"},
	"VG": {Code: "VG", CName: "英属维尔京群岛", EName: "Virgin Islands (British)"},
	"VI": {Code: "VI", CName: "美属维尔京群岛", EName: "Virgin Islands (U.S.)"},
	"WF": {Code: "WF", CName: "瓦利斯和富图纳", EName: "Wallis and Futuna"},
	"EH": {Code: "EH", CName: "西撒哈拉", EName: "Western Sahara"},
	"YE": {Code: "YE", CName: "也门", EName: "Yemen"},
	"ZM": {Code: "ZM", CName: "赞比亚", EName: "Zambia"},
	"ZW": {Code: "ZW", CName: "津巴布韦", EName: "Zimbabwe"},
	"AX": {Code: "AX", CName: "奥兰", EName: "Åland Islands"},
}

// 查询单个国家的资产统计
func fetchCountryStats(ctx context.Context, countryCode string) (*FofaResp, error) {
	// 构建查询条件: country="XX" && port!=""
	query := fmt.Sprintf(`country="%s" && port!=""`, countryCode)
	qbase64 := base64.StdEncoding.EncodeToString([]byte(query))

	url := fmt.Sprintf("https://fofa.info/api/v1/search/stats?fields=country&key=a5badb45e12cd17414ebae4ea333c4f9&qbase64=%s", qbase64)

	g.Log().Infof(ctx, "正在查询国家 %s 的资产统计...", countryCode)

	resp, err := http.Get(url)
	if err != nil {
		return nil, gerror.Newf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, gerror.Newf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var fofaResp FofaResp
	if err := json.NewDecoder(resp.Body).Decode(&fofaResp); err != nil {
		return nil, gerror.Newf("JSON解析失败: %v", err)
	}

	if fofaResp.Error {
		return nil, gerror.Newf("FOFA API返回错误: %s", fofaResp.Query)
	}

	return &fofaResp, nil
}

// 批量查询所有国家的资产统计
func fetchAndStoreFofaStats(ctx context.Context) error {
	today := time.Now().Format("2006-01-02")
	totalSuccessCount := 0
	totalCountries := len(GlobalCountries)

	g.Log().Infof(ctx, "开始批量查询全球 %d 个国家和地区的资产统计...", totalCountries)

	// 检查今天是否已经有数据，避免重复插入
	var existingCount int
	count, err := g.DB().Model("asset_stats").Where("stat_date = ?", today).Count()
	if err != nil {
		g.Log().Warning(ctx, "检查现有数据失败: ", err)
	} else {
		existingCount = count
		if existingCount > 0 {
			g.Log().Infof(ctx, "今天已有 %d 条数据，将跳过已存在的国家", existingCount)
		}
	}

	for i, countryCode := range GlobalCountries {
		// 检查该国家今天是否已有数据
		if existingCount > 0 {
			var exists int
			count, err := g.DB().Model("asset_stats").
				Where("country_code = ? AND stat_date = ?", countryCode, today).
				Count()
			if err == nil && count > 0 {
				g.Log().Infof(ctx, "国家 %s 今天已有数据，跳过", countryCode)
				continue
			}
		}

		// 查询单个国家的数据
		fofaResp, err := fetchCountryStats(ctx, countryCode)
		if err != nil {
			g.Log().Errorf(ctx, "查询国家 %s 失败: %v", countryCode, err)
			continue
		}

		// 处理返回的数据
		if len(fofaResp.Aggs.Countries) == 0 {
			g.Log().Infof(ctx, "国家 %s 没有资产数据", countryCode)
			continue
		}

		// 插入数据库
		for _, c := range fofaResp.Aggs.Countries {
			g.Log().Infof(ctx, "插入国家数据: %s (%s) - %d", c.Name, c.NameCode, c.Count)

			_, err := g.DB().Model("asset_stats").Data(g.Map{
				"country_name": c.Name,
				"country_code": c.NameCode,
				"asset_count":  c.Count,
				"stat_date":    today,
			}).Insert()
			if err != nil {
				g.Log().Error(ctx, "数据库插入失败: ", err)
			} else {
				totalSuccessCount++
			}
		}

		// 进度提示
		if (i+1)%10 == 0 || i+1 == totalCountries {
			g.Log().Infof(ctx, "进度: %d/%d 国家已处理，成功插入 %d 条记录",
				i+1, totalCountries, totalSuccessCount)
		}

		// 添加延迟避免API限制
		time.Sleep(100 * time.Millisecond)
	}

	g.Log().Infof(ctx, "批量查询完成，总共成功插入 %d 条记录", totalSuccessCount)
	return nil
}

func main() {
	ctx := context.Background()

	// 定时任务：每天0点拉取并存储数据
	// gtimer.AddSingleton(ctx, time.Minute, func(ctx context.Context) {
	// 	now := time.Now()
	// 	if now.Hour() == 0 && now.Minute() == 0 {
	// 		err := fetchAndStoreFofaStats(ctx)
	// 		if err != nil {
	// 			g.Log().Error(ctx, "定时任务失败: ", err)
	// 		}
	// 	}
	// })

	// 启动时执行一次数据拉取（可选，取消注释以启用）
	// err := fetchAndStoreFofaStats(ctx)
	// if err != nil {
	// 	g.Log().Error(ctx, "定时任务失败: ", err)
	// }

	// HTTP接口：按国家返回历史数据
	s := ghttp.GetServer()
	s.SetPort(8001)
	s.BindHandler("/api/assets", func(r *ghttp.Request) {
		country := r.Get("country").String()
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Where("country_code = ?", country).
			Order("stat_date asc").
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}

		// 转换为历史数据格式
		var history []g.Map
		for _, item := range result {
			history = append(history, g.Map{
				"stat_date":    item.StatDate,
				"asset_count":  item.AssetCount,
			})
		}

		r.Response.WriteJson(g.Map{
			"country": country,
			"history": history,
		})
	})

	// 添加调试接口查看所有数据
	s.BindHandler("/api/debug/all", func(r *ghttp.Request) {
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Order("created_at desc").
			Limit(20).
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}
		r.Response.WriteJson(g.Map{
			"total": len(result),
			"data":  result,
		})
	})

	// 手动触发批量查询接口
	s.BindHandler("/api/fetch/all", func(r *ghttp.Request) {
		go func() {
			ctx := context.Background()
			err := fetchAndStoreFofaStats(ctx)
			if err != nil {
				g.Log().Error(ctx, "批量查询失败: ", err)
			}
		}()

		r.Response.WriteJson(g.Map{
			"message": "批量查询已启动，请查看日志获取进度",
			"status":  "started",
		})
	})

	// 查看统计信息接口
	s.BindHandler("/api/stats/summary", func(r *ghttp.Request) {
		// 按日期统计
		var dateStats []g.Map
		err := g.DB().Model("asset_stats").
			Fields("stat_date, COUNT(*) as country_count, SUM(asset_count) as total_assets").
			Group("stat_date").
			Order("stat_date desc").
			Scan(&dateStats)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}

		// 总统计
		var totalCountries int
		var totalAssets int64
		totalCountries, _ = g.DB().Model("asset_stats").Count()
		g.DB().Model("asset_stats").Fields("SUM(asset_count)").Scan(&totalAssets)

		r.Response.WriteJson(g.Map{
			"total_countries": totalCountries,
			"total_assets":    totalAssets,
			"by_date":         dateStats,
		})
	})

	s.Run()
}
