package main

import (
	"context"
	"encoding/json"
	"net/http"
	"time"


		_ "github.com/gogf/gf/contrib/drivers/mysql/v2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	//"github.com/gogf/gf/v2/os/gtimer"
)

// 资产统计结构体
// 可根据实际API返回结构调整
// 这里只做演示

type AssetStat struct {
	Country       string `json:"country"`
	AssetCount    int    `json:"asset_count"`
	UniqueIPCount int    `json:"unique_ip_count"`
	Date          string `json:"date"`
}

type FofaCountry struct {
	Code     string `json:"code"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
	NameCode string `json:"name_code"`
}

type FofaAggs struct {
	Countries []FofaCountry `json:"countries"`
}

type FofaResp struct {
	Error          bool     `json:"error"`
	Aggs           FofaAggs `json:"aggs"`
	Query          string   `json:"query"`
	LastUpdateTime string   `json:"lastupdatetime"`
}

func fetchAndStoreFofaStats(ctx context.Context) error {
	url := "https://fofa.info/api/v1/search/stats?&size=10000&key=&qbase64=cG9ydCE9IiIg"
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	var fofaResp FofaResp
	if err := json.NewDecoder(resp.Body).Decode(&fofaResp); err != nil {
		return err
	}
	if fofaResp.Error {
		return gerror.New("FOFA API error")
	}
	today := time.Now().Format("2006-01-02")
	for _, c := range fofaResp.Aggs.Countries {
		_, err := g.DB().Model("asset_stats").Data(g.Map{
			"country_name": c.Name,
			"country_code": c.NameCode,
			"asset_count":  c.Count,
			"stat_date":    today,
		}).Insert()
		if err != nil {
			g.Log().Error(ctx, "数据库插入失败: ", err)
		}
	}
	return nil
}

func main() {
	ctx := context.Background()

	// 定时任务：每天0点拉取并存储数据
	// gtimer.AddSingleton(ctx, time.Minute, func(ctx context.Context) {
	// 	now := time.Now()
	// 	if now.Hour() == 0 && now.Minute() == 0 {
	// 		err := fetchAndStoreFofaStats(ctx)
	// 		if err != nil {
	// 			g.Log().Error(ctx, "定时任务失败: ", err)
	// 		}
	// 	}
	// })

				err := fetchAndStoreFofaStats(ctx)
			if err != nil {
				g.Log().Error(ctx, "定时任务失败: ", err)
			}

	// HTTP接口：按国家返回历史数据
	s := ghttp.GetServer()
	s.SetPort(8000)
	s.BindHandler("/api/assets", func(r *ghttp.Request) {
		country := r.Get("country").String()
		var result []g.Map
		err := g.DB().Model("asset_stats").
			Where("country_code = ?", country).
			Order("stat_date asc").
			Fields("stat_date, asset_count").
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			return
		}
		r.Response.WriteJson(g.Map{
			"country": country,
			"history": result,
		})
	})

	s.Run()
}
