package main

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	//"github.com/gogf/gf/v2/os/gtimer"
)

// 资产统计结构体
// 可根据实际API返回结构调整
// 这里只做演示

type AssetStat struct {
	Country       string `json:"country"`
	AssetCount    int    `json:"asset_count"`
	UniqueIPCount int    `json:"unique_ip_count"`
	Date          string `json:"date"`
}

type FofaCountry struct {
	Code     string `json:"code"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
	NameCode string `json:"name_code"`
}

type FofaAggs struct {
	Countries []FofaCountry `json:"countries"`
}

type FofaResp struct {
	Error          bool     `json:"error"`
	Aggs           FofaAggs `json:"aggs"`
	Query          string   `json:"query"`
	LastUpdateTime string   `json:"lastupdatetime"`
}

func fetchAndStoreFofaStats(ctx context.Context) error {
	// 使用更具体的查询来获取国家统计信息
	url := "https://fofa.info/api/v1/search/stats?&size=10000&key=a5badb45e12cd17414ebae4ea333c4f9&qbase64=cG9ydCE9IiIg&fields=country"

	g.Log().Info(ctx, "正在调用FOFA API...")

	resp, err := http.Get(url)
	if err != nil {
		return gerror.Newf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return gerror.Newf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var fofaResp FofaResp
	if err := json.NewDecoder(resp.Body).Decode(&fofaResp); err != nil {
		return gerror.Newf("JSON解析失败: %v", err)
	}

	g.Log().Infof(ctx, "FOFA API响应: error=%v, size=%d, countries_count=%d",
		fofaResp.Error, len(fofaResp.Aggs.Countries), len(fofaResp.Aggs.Countries))

	if fofaResp.Error {
		return gerror.New("FOFA API返回错误状态")
	}

	if len(fofaResp.Aggs.Countries) == 0 {
		g.Log().Warning(ctx, "FOFA API返回的国家数据为空，可能是查询条件问题或数据暂时不可用")
		return nil // 不返回错误，只是警告
	}

	today := time.Now().Format("2006-01-02")
	successCount := 0

	for _, c := range fofaResp.Aggs.Countries {
		_, err := g.DB().Model("asset_stats").Data(g.Map{
			"country_name": c.Name,
			"country_code": c.NameCode,
			"asset_count":  c.Count,
			"stat_date":    today,
		}).Insert()
		if err != nil {
			g.Log().Error(ctx, "数据库插入失败: ", err)
		} else {
			successCount++
		}
	}

	g.Log().Infof(ctx, "数据插入完成，成功插入%d条记录", successCount)
	return nil
}

func main() {
	ctx := context.Background()

	// 定时任务：每天0点拉取并存储数据
	// gtimer.AddSingleton(ctx, time.Minute, func(ctx context.Context) {
	// 	now := time.Now()
	// 	if now.Hour() == 0 && now.Minute() == 0 {
	// 		err := fetchAndStoreFofaStats(ctx)
	// 		if err != nil {
	// 			g.Log().Error(ctx, "定时任务失败: ", err)
	// 		}
	// 	}
	// })

				err := fetchAndStoreFofaStats(ctx)
			if err != nil {
				g.Log().Error(ctx, "定时任务失败: ", err)
			}

	// HTTP接口：按国家返回历史数据
	s := ghttp.GetServer()
	s.SetPort(8001)
	s.BindHandler("/api/assets", func(r *ghttp.Request) {
		country := r.Get("country").String()
		var result []g.Map
		err := g.DB().Model("asset_stats").
			Where("country_code = ?", country).
			Order("stat_date asc").
			Fields("stat_date, asset_count").
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			return
		}
		r.Response.WriteJson(g.Map{
			"country": country,
			"history": result,
		})
	})

	s.Run()
}
