package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	//"github.com/gogf/gf/v2/os/gtimer"
)

// 资产统计结构体
// 可根据实际API返回结构调整
// 这里只做演示

type AssetStat struct {
	ID          int    `json:"id" orm:"id"`
	CountryName string `json:"country_name" orm:"country_name"`
	CountryCode string `json:"country_code" orm:"country_code"`
	AssetCount  int64  `json:"asset_count" orm:"asset_count"`
	StatDate    string `json:"stat_date" orm:"stat_date"`
	CreatedAt   string `json:"created_at" orm:"created_at"`
}

type FofaCountry struct {
	Code     string `json:"code"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
	NameCode string `json:"name_code"`
}

type FofaAggs struct {
	Countries []FofaCountry `json:"countries"`
}

type FofaResp struct {
	Error          bool     `json:"error"`
	Aggs           FofaAggs `json:"aggs"`
	Query          string   `json:"query"`
	LastUpdateTime string   `json:"lastupdatetime"`
}

// 全球国家和地区映射
var GlobalCountries = []string{
	"AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AQ", "AR", "AS", "AT", "AU", "AW", "AX", "AZ",
	"BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BL", "BM", "BN", "BO", "BQ", "BR", "BS",
	"BT", "BV", "BW", "BY", "BZ", "CA", "CC", "CD", "CF", "CG", "CH", "CI", "CK", "CL", "CM", "CN",
	"CO", "CR", "CU", "CV", "CW", "CX", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE",
	"EG", "EH", "ER", "ES", "ET", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF",
	"GG", "GH", "GI", "GL", "GM", "GN", "GP", "GQ", "GR", "GS", "GT", "GU", "GW", "GY", "HK", "HM",
	"HN", "HR", "HT", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IQ", "IR", "IS", "IT", "JE", "JM",
	"JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KP", "KR", "KW", "KY", "KZ", "LA", "LB", "LC",
	"LI", "LK", "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK",
	"ML", "MM", "MN", "MO", "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA",
	"NC", "NE", "NF", "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG",
	"PH", "PK", "PL", "PM", "PN", "PR", "PS", "PT", "PW", "PY", "QA", "RE", "RO", "RS", "RU", "RW",
	"SA", "SB", "SC", "SD", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SO", "SR", "SS",
	"ST", "SV", "SX", "SY", "SZ", "TC", "TD", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TO",
	"TR", "TT", "TV", "TW", "TZ", "UA", "UG", "UM", "US", "UY", "UZ", "VA", "VC", "VE", "VG", "VI",
	"VN", "VU", "WF", "WS", "YE", "YT", "ZA", "ZM", "ZW",
}

// 查询单个国家的资产统计
func fetchCountryStats(ctx context.Context, countryCode string) (*FofaResp, error) {
	// 构建查询条件: country="XX" && port!=""
	query := fmt.Sprintf(`country="%s" && port!=""`, countryCode)
	qbase64 := base64.StdEncoding.EncodeToString([]byte(query))

	url := fmt.Sprintf("https://fofa.info/api/v1/search/stats?fields=country&key=a5badb45e12cd17414ebae4ea333c4f9&qbase64=%s", qbase64)

	g.Log().Infof(ctx, "正在查询国家 %s 的资产统计...", countryCode)

	resp, err := http.Get(url)
	if err != nil {
		return nil, gerror.Newf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, gerror.Newf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var fofaResp FofaResp
	if err := json.NewDecoder(resp.Body).Decode(&fofaResp); err != nil {
		return nil, gerror.Newf("JSON解析失败: %v", err)
	}

	if fofaResp.Error {
		return nil, gerror.Newf("FOFA API返回错误: %s", fofaResp.Query)
	}

	return &fofaResp, nil
}

// 批量查询所有国家的资产统计
func fetchAndStoreFofaStats(ctx context.Context) error {
	today := time.Now().Format("2006-01-02")
	totalSuccessCount := 0
	totalCountries := len(GlobalCountries)

	g.Log().Infof(ctx, "开始批量查询全球 %d 个国家和地区的资产统计...", totalCountries)

	// 检查今天是否已经有数据，避免重复插入
	var existingCount int
	count, err := g.DB().Model("asset_stats").Where("stat_date = ?", today).Count()
	if err != nil {
		g.Log().Warning(ctx, "检查现有数据失败: ", err)
	} else {
		existingCount = count
		if existingCount > 0 {
			g.Log().Infof(ctx, "今天已有 %d 条数据，将跳过已存在的国家", existingCount)
		}
	}

	for i, countryCode := range GlobalCountries {
		// 检查该国家今天是否已有数据
		if existingCount > 0 {
			var exists int
			count, err := g.DB().Model("asset_stats").
				Where("country_code = ? AND stat_date = ?", countryCode, today).
				Count()
			if err == nil && count > 0 {
				g.Log().Infof(ctx, "国家 %s 今天已有数据，跳过", countryCode)
				continue
			}
		}

		// 查询单个国家的数据
		fofaResp, err := fetchCountryStats(ctx, countryCode)
		if err != nil {
			g.Log().Errorf(ctx, "查询国家 %s 失败: %v", countryCode, err)
			continue
		}

		// 处理返回的数据
		if len(fofaResp.Aggs.Countries) == 0 {
			g.Log().Infof(ctx, "国家 %s 没有资产数据", countryCode)
			continue
		}

		// 插入数据库
		for _, c := range fofaResp.Aggs.Countries {
			g.Log().Infof(ctx, "插入国家数据: %s (%s) - %d", c.Name, c.NameCode, c.Count)

			_, err := g.DB().Model("asset_stats").Data(g.Map{
				"country_name": c.Name,
				"country_code": c.NameCode,
				"asset_count":  c.Count,
				"stat_date":    today,
			}).Insert()
			if err != nil {
				g.Log().Error(ctx, "数据库插入失败: ", err)
			} else {
				totalSuccessCount++
			}
		}

		// 进度提示
		if (i+1)%10 == 0 || i+1 == totalCountries {
			g.Log().Infof(ctx, "进度: %d/%d 国家已处理，成功插入 %d 条记录",
				i+1, totalCountries, totalSuccessCount)
		}

		// 添加延迟避免API限制
		time.Sleep(100 * time.Millisecond)
	}

	g.Log().Infof(ctx, "批量查询完成，总共成功插入 %d 条记录", totalSuccessCount)
	return nil
}

func main() {
	ctx := context.Background()

	// 定时任务：每天0点拉取并存储数据
	// gtimer.AddSingleton(ctx, time.Minute, func(ctx context.Context) {
	// 	now := time.Now()
	// 	if now.Hour() == 0 && now.Minute() == 0 {
	// 		err := fetchAndStoreFofaStats(ctx)
	// 		if err != nil {
	// 			g.Log().Error(ctx, "定时任务失败: ", err)
	// 		}
	// 	}
	// })

	// 启动时执行一次数据拉取（可选，取消注释以启用）
	// err := fetchAndStoreFofaStats(ctx)
	// if err != nil {
	// 	g.Log().Error(ctx, "定时任务失败: ", err)
	// }

	// HTTP接口：按国家返回历史数据
	s := ghttp.GetServer()
	s.SetPort(8001)
	s.BindHandler("/api/assets", func(r *ghttp.Request) {
		country := r.Get("country").String()
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Where("country_code = ?", country).
			Order("stat_date asc").
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}

		// 转换为历史数据格式
		var history []g.Map
		for _, item := range result {
			history = append(history, g.Map{
				"stat_date":    item.StatDate,
				"asset_count":  item.AssetCount,
			})
		}

		r.Response.WriteJson(g.Map{
			"country": country,
			"history": history,
		})
	})

	// 添加调试接口查看所有数据
	s.BindHandler("/api/debug/all", func(r *ghttp.Request) {
		var result []AssetStat
		err := g.DB().Model("asset_stats").
			Order("created_at desc").
			Limit(20).
			Scan(&result)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}
		r.Response.WriteJson(g.Map{
			"total": len(result),
			"data":  result,
		})
	})

	// 手动触发批量查询接口
	s.BindHandler("/api/fetch/all", func(r *ghttp.Request) {
		go func() {
			ctx := context.Background()
			err := fetchAndStoreFofaStats(ctx)
			if err != nil {
				g.Log().Error(ctx, "批量查询失败: ", err)
			}
		}()

		r.Response.WriteJson(g.Map{
			"message": "批量查询已启动，请查看日志获取进度",
			"status":  "started",
		})
	})

	// 查看统计信息接口
	s.BindHandler("/api/stats/summary", func(r *ghttp.Request) {
		// 按日期统计
		var dateStats []g.Map
		err := g.DB().Model("asset_stats").
			Fields("stat_date, COUNT(*) as country_count, SUM(asset_count) as total_assets").
			Group("stat_date").
			Order("stat_date desc").
			Scan(&dateStats)
		if err != nil {
			r.Response.WriteStatus(500)
			r.Response.WriteJson(g.Map{"error": err.Error()})
			return
		}

		// 总统计
		var totalCountries int
		var totalAssets int64
		totalCountries, _ = g.DB().Model("asset_stats").Count()
		g.DB().Model("asset_stats").Fields("SUM(asset_count)").Scan(&totalAssets)

		r.Response.WriteJson(g.Map{
			"total_countries": totalCountries,
			"total_assets":    totalAssets,
			"by_date":         dateStats,
		})
	})

	s.Run()
}
